<?xml version="1.0"?>
<ruleset name="Security sniffs from WordPress Coding Standards">
  <description>Security sniffs from WordPress Coding Standards</description>

  <arg value="sp"/>
  <arg name="colors"/>
  <arg name="extensions" value="php"/>
  <arg name="parallel" value="8"/>

  <config name="testVersion" value="7.2-"/>

  <!-- Do not fail PHPCS CI over warnings -->
  <config name="ignore_warnings_on_exit" value="1"/>

  <rule ref="WordPress.Security.EscapeOutput"/>
  <rule ref="WordPress.Security.EscapeOutput">
    <properties>
      <property name="customEscapingFunctions" type="array" value="wc_help_tip,wc_sanitize_tooltip,wc_selected,wc_kses_notice,wc_esc_json,wc_query_string_form_fields,wc_make_phone_clickable" />
    </properties>
  </rule>
  <rule ref="WordPress.Security.ValidatedSanitizedInput.InputNotSanitized"/>
  <rule ref="WordPress.Security.ValidatedSanitizedInput">
    <properties>
      <property name="customSanitizingFunctions" type="array" value="wc_clean,wc_sanitize_tooltip,wc_format_decimal,wc_stock_amount,wc_sanitize_permalink,wc_sanitize_textarea" />
    </properties>
  </rule>
  <!-- Encourage use of wp_safe_redirect() to avoid open redirect vulnerabilities.
     https://github.com/WordPress/WordPress-Coding-Standards/pull/1264 -->
  <rule ref="WordPress.Security.SafeRedirect"/>

  <!-- Verify that a nonce check is done before using values in superglobals.
     https://github.com/WordPress/WordPress-Coding-Standards/issues/73 -->
  <rule ref="WordPress.Security.NonceVerification"/>

  <!-- https://github.com/WordPress/WordPress-Coding-Standards/issues/1157 -->
  <rule ref="WordPress.Security.PluginMenuSlug"/>

  <!-- Covers rule: The eval() construct is very dangerous, and is impossible to secure. ... these must not be used. -->
  <rule ref="Squiz.PHP.Eval.Discouraged">
    <type>error</type>
    <message>eval() is a security risk so not allowed.</message>
  </rule>

</ruleset>
