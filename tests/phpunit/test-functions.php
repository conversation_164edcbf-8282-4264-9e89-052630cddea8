<?php
/**
 * The FunctionsTests class tests the functions on file functions.php.
 *
 * @package WooCommerce_Warranty
 */

use PHPUnit\Framework\TestCase;
/**
 * The FunctionsTests class tests the functions on file functions.php.
 */
class FunctionsTests extends TestCase {
	/**
	 * Set up required options before each test.
	 *
	 * @since 1.9.9
	 *
	 * @return void
	 */
	public function setUp(): void {
		\WP_Mock::setUp();
	}
	/**
	 * Remove required options after each test.
	 *
	 * @since 1.9.9
	 *
	 * @return void
	 */
	public function tearDown(): void {
		\WP_Mock::tearDown();
	}

	/**
	 * Test function warranty_stripslashes strips slashes.
	 *
	 * @since 1.9.9
	 */
	public function test_warranty_stripslashes_strips_slashes_from_string() {

		$string          = 'My \String';
		$expected_string = 'My String';

		$output = warranty_stripslashes( $string );

		$this->assertEquals( $output, $expected_string );
	}

	/**
	 * Test function warranty_trim_duration removes s if singular.
	 *
	 * @since 1.9.9
	 */
	public function test_warranty_trim_duration_removes_s_if_singular() {

		$duration                   = 'days';
		$singular_value             = 1;
		$expected_singular_duration = 'day';

		$output = warranty_trim_duration( $duration, $singular_value );

		$this->assertEquals( $output, $expected_singular_duration );
	}

	/**
	 * Test function warranty_trim_duration does nothing if plural.
	 *
	 * @since 1.9.9
	 */
	public function test_warranty_trim_duration_does_nothing_if_plural() {

		$duration                 = 'days';
		$plural_value             = 2;
		$expected_plural_duration = 'days';

		$output = warranty_trim_duration( $duration, $plural_value );

		$this->assertEquals( $output, $expected_plural_duration );
	}

	/**
	 * Test function warranty_get_order_item_warranty returns expected values for default warranty.
	 *
	 * @since 1.9.9
	 */
	public function test_warranty_get_order_item_warranty_returns_default_warranty() {

		$item_meta = array(
			'otherDummyMeta' => array(),
			'_item_warranty' => array(
				'type'               => 'included_warranty',
				'label'              => 'Warranty',
				'length'             => 'limited',
				'value'              => '30',
				'duration'           => 'days',
				'no_warranty_option' => 'yes',
				'addons'             => array(),
				'default'            => true,
			),
		);
		\WP_Mock::userFunction( 'maybe_unserialize', array( 'return' => $item_meta['_item_warranty'] ) );
		$item['item_meta'] = $item_meta;
		$warranty          = warranty_get_order_item_warranty( $item );
		$expected_warranty = $item_meta['_item_warranty'];
		$this->assertEquals( $expected_warranty, $warranty );
	}

	/**
	 * Test function warranty_get_order_item_warranty returns expected values for item included warranty.
	 *
	 * @since 1.9.9
	 */
	public function test_warranty_get_order_item_warranty_returns_included_warranty() {
		$item_meta = array(
			'dummyMeta'      => 'dummyData',
			'otherDummyMeta' => array(),
			'_item_warranty' => array(
				'type'     => 'included_warranty',
				'label'    => 'Warranty',
				'length'   => 'limited',
				'value'    => '30',
				'duration' => 'days',
			),
		);
		\WP_Mock::userFunction( 'maybe_unserialize', array( 'return' => $item_meta['_item_warranty'] ) );
		$item['item_meta'] = $item_meta;
		$warranty          = warranty_get_order_item_warranty( $item );
		$expected_warranty = $item_meta['_item_warranty'];
		$this->assertEquals( $expected_warranty, $warranty );
	}

	/**
	 * Test function warranty_get_order_item_optional_addon_warranty returns expected values for optional addon item warranty.
	 *
	 * @since 1.9.9
	 */
	public function test_warranty_get_order_item_warranty_returns_item_addon_warranty() {
		$item_meta = array(
			'dummyMeta'               => 'dummyData',
			'otherDummyMeta'          => array(),
			'_item_warranty_selected' => '1',
			'_item_warranty'          => array(
				'type'               => 'addon_warranty',
				'addons'             => array(
					0 => array(
						'amount'   => '30',
						'value'    => '30',
						'duration' => 'days',
					),
					1 => array(
						'amount'   => '40',
						'value'    => '2',
						'duration' => 'months',
					),
					2 => array(
						'amount'   => '50',
						'value'    => '1',
						'duration' => 'years',
					),
				),
				'no_warranty_option' => 'yes',
				'label'              => 'Warranty',
			),
		);
		\WP_Mock::userFunction( 'maybe_unserialize', array( 'return' => $item_meta['_item_warranty'] ) );

		$item['item_meta']                 = $item_meta;
		$warranty                          = warranty_get_order_item_warranty( $item );
		$expected_warranty                 = $item_meta['_item_warranty'];
		$expected_warranty['warranty_idx'] = '1';
		$this->assertEquals( $expected_warranty, $warranty );
	}
}
