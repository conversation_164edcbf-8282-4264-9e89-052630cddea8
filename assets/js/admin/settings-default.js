jQuery( 'document' ).ready( function( $ ) {
	$( '#warranty_default_type' ).change( function() {
		var show_if_addon_warranty    = $( '.show-if-addon_warranty' ),
			show_if_included_warranty = $( '.show-if-included_warranty' );

		show_if_addon_warranty.parents( 'tr' ).hide();
		show_if_included_warranty.parents( 'tr' ).hide();

		switch ( $( this ).val() ) {

			case 'included_warranty':
				show_if_included_warranty.parents( 'tr' ).show();
				break;

			case 'addon_warranty':
				show_if_addon_warranty.parents( 'tr' ).show();
				break;

		}
	} ).change();

	$( '#warranty_default_length' ).change( function() {
		if ( 'limited' === $( this ).val() ) {
			$( '#warranty_default_length_value' ).parents( 'tr' ).show();
			$( '#warranty_default_length_duration' ).parents( 'tr' ).show();
		} else {
			$( '#warranty_default_length_value' ).parents( 'tr' ).hide();
			$( '#warranty_default_length_duration' ).parents( 'tr' ).hide();
		}
	} ).change();
} );