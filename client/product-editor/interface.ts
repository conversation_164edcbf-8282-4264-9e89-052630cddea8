export type WarrantyDuration = 'days' | 'weeks' | 'months' | 'years';

export interface WarrantyAddon {
	amount: number;
	value: string;
	duration: WarrantyDuration;
}

export interface Warranty {
	type: 'no_warranty' | 'included_warranty' | 'addon_warranty' | '';
	length: 'lifetime' | 'limited' | '';
	value: string;
	duration: WarrantyDuration;
	no_warranty_option: string;
	addons: WarrantyAddon[];
}
