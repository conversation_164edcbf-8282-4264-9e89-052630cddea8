import type { BlockAttributes } from '@wordpress/blocks';
import {
	createElement,
	useCallback,
	useEffect,
	useRef,
	useState,
	useMemo,
} from '@wordpress/element';
import { useWooBlockProps } from '@woocommerce/block-templates';
// @ts-ignore
import { useEntityProp, useEntityId } from '@wordpress/core-data';
import { __experimentalUseProductEntityProp as useProductEntityProp } from '@woocommerce/product-editor';
import apiFetch from '@wordpress/api-fetch';

import { Warranty, WarrantyAddon, WarrantyDuration } from './interface';
import {
	TextControl,
	SelectControl,
	CheckboxControl,
	Button,
} from '@wordpress/components';
import { getNewPath } from '@woocommerce/navigation';
import { __ } from '@wordpress/i18n';

export function Edit( {
	attributes,
	context,
}: {
	attributes: BlockAttributes;
	context: { postType: string };
} ) {
	/**
	 * React hook that is used to mark the block wrapper element.
	 * It provides all the necessary props like the class name.
	 *
	 * @see https://developer.wordpress.org/block-editor/reference-guides/packages/packages-block-editor/#useblockprops
	 */
	const wooBlockProps = useWooBlockProps( attributes );

	const postType = context.postType;

	const [ productType ] = useProductEntityProp< string >( 'type', {
		postType,
	} );

	const isVariableProduct = productType === 'variable';
	const isProductVariation = postType === 'product_variation';
	const productId = useEntityId( 'postType', 'product' );
	const [ parentId ] = useEntityProp< number >(
		'postType',
		postType,
		'parent_id'
	);
	const [ parentMeta ] = useEntityProp(
		'postType',
		'product',
		'meta_data',
		parentId
	);

	const initialWarrantyState = useMemo< Warranty >(
		() => ( {
			type: '',
			length: '',
			value: '',
			duration: 'days',
			no_warranty_option: '',
			addons: [],
		} ),
		[]
	);
	const defaultWarranty = useRef< Warranty | null >( null );
	const [
		parentWarrantyControlMetaValue,
		setParentWarrantyControlMetaValue,
	] = useState< { value: string } | null >( null );
	const [ warranty, setWarranty ] =
		useState< Warranty >( initialWarrantyState );

	const [ warrantyMetaValue, setWarrantyMetaValue ] =
		useProductEntityProp< Warranty | null >( 'meta_data._warranty', {
			postType,
		} );

	const [ warrantyLabelMetaValue, setWarrantyLabelMetaValue ] =
		useProductEntityProp< string >( 'meta_data._warranty_label', {
			postType,
		} );

	const [ warrantyControlMetaValue, setWarrantyControlMetaValue ] =
		useProductEntityProp< 'parent' | 'variations' >(
			'meta_data._warranty_control',
			{
				postType,
				fallbackValue: 'parent',
			}
		);

	// Check if warranty settings are required for the variation.
	const isWarrantySettingsRequiredForVariation = useMemo( () => {
		return (
			isProductVariation &&
			parentWarrantyControlMetaValue?.value === 'variations'
		);
	}, [ isProductVariation, parentWarrantyControlMetaValue ] );

	// Check if warranty settings are required.
	const isWarrantySettingsRequired = useMemo( () => {
		if ( ! isVariableProduct && ! isProductVariation ) return true;
		if ( isVariableProduct && warrantyControlMetaValue === 'parent' )
			return true;
		return isWarrantySettingsRequiredForVariation;
	}, [
		isVariableProduct,
		isProductVariation,
		warrantyControlMetaValue,
		isWarrantySettingsRequiredForVariation,
	] );

	// Set the parent warranty control meta value when the parent meta is available.
	useEffect( () => {
		if ( isProductVariation && parentMeta ) {
			setParentWarrantyControlMetaValue(
				parentMeta.find(
					( meta: { key: string } ) =>
						meta.key === '_warranty_control'
				)
			);
		}
	}, [ isProductVariation, parentMeta ] );

	// Set the warranty data when the meta value is available.
	useEffect( () => {
		if ( warrantyMetaValue ) {
			setWarranty( warrantyMetaValue );
		} else {
			setWarranty( initialWarrantyState );
		}
	}, [ initialWarrantyState, warrantyMetaValue ] );

	// Fetch the default warranty data.
	useEffect( () => {
		const id = parentId ?? productId;
		apiFetch< Warranty >( {
			path: `/wc/v3/default_warranty/` + id,
		} )
			.then( ( response ) => {
				defaultWarranty.current = response;
			} )
			.catch( () => ( defaultWarranty.current = initialWarrantyState ) );
	}, [ initialWarrantyState, parentId, productId ] );

	// Update the warranty data.
	const updateWarranty = useCallback(
		( updatedFields: Partial< Warranty > ) => {
			setWarrantyMetaValue( {
				...warranty,
				...updatedFields,
			} );
		},
		[ setWarrantyMetaValue, warranty ]
	);

	return (
		<div { ...wooBlockProps }>
			{ isVariableProduct && (
				<SelectControl
					label={ __( 'Warranty Control', 'woocommerce-warranty' ) }
					value={ warrantyControlMetaValue || 'parent' }
					options={ [
						{
							label: __(
								'Define warranty for all variations',
								'woocommerce-warranty'
							),
							value: 'parent',
						},
						{
							label: __(
								'Define warranty per variation',
								'woocommerce-warranty'
							),
							value: 'variations',
						},
					] }
					onChange={ ( value ) =>
						setWarrantyControlMetaValue( value )
					}
				/>
			) }

			{ isProductVariation &&
				! isWarrantySettingsRequiredForVariation && (
					<div className="woocommerce-product-notice info">
						<p className="woocommerce-product-notice__content">
							{ __(
								'Warranty settings are managed at the parent product level. Because the "Warranty Settings Control" is set to "Define warranty for all variations", individual variation warranty options cannot be modified.',
								'woocommerce-warranty'
							) }
							&nbsp;
							<a
								href={ getNewPath(
									{ tab: 'warranty-group' },
									`/product/${ parentId }`
								) }
							>
								{ __(
									'Modify parent product warranty settings.',
									'woocommerce-warranty'
								) }
							</a>
						</p>
					</div>
				) }
			{ isWarrantySettingsRequired && (
				<div>
					<CheckboxControl
						label={ __(
							'Default Product Warranty',
							'woocommerce-warranty'
						) }
						checked={ ! warranty.type }
						onChange={ ( isChecked ) => {
							if ( isChecked ) {
								setWarrantyMetaValue( null );
							} else {
								setWarrantyMetaValue( defaultWarranty.current );
							}
						} }
					/>
					<hr />
					{ warranty.type && (
						<div>
							<SelectControl
								label={ __(
									'Product Warranty',
									'woocommerce-warranty'
								) }
								value={ warranty.type }
								options={ [
									{
										label: __(
											'Select Warranty Type',
											'woocommerce-warranty'
										),
										value: '',
									},
									{
										label: __(
											'No Warranty',
											'woocommerce-warranty'
										),
										value: 'no_warranty',
									},
									{
										label: __(
											'Warranty Included',
											'woocommerce-warranty'
										),
										value: 'included_warranty',
									},
									{
										label: __(
											'Warranty as Add-On',
											'woocommerce-warranty'
										),
										value: 'addon_warranty',
									},
								] }
								onChange={ ( value ) =>
									updateWarranty( {
										type: value as Warranty[ 'type' ],
									} )
								}
							/>
							{ warranty.type !== 'no_warranty' && (
								<TextControl
									label={ __(
										'Warranty Label',
										'woocommerce-warranty'
									) }
									value={ warrantyLabelMetaValue || '' }
									onChange={ ( value ) =>
										setWarrantyLabelMetaValue( value )
									}
								/>
							) }
							{ warranty.type === 'included_warranty' && (
								<div>
									<SelectControl
										label={ __(
											'Warranty Length',
											'woocommerce-warranty'
										) }
										value={ warranty.length }
										options={ [
											{
												label: __(
													'Select Warranty Length',
													'woocommerce-warranty'
												),
												value: '',
											},
											{
												label: __(
													'Lifetime',
													'woocommerce-warranty'
												),
												value: 'lifetime',
											},
											{
												label: __(
													'Limited',
													'woocommerce-warranty'
												),
												value: 'limited',
											},
										] }
										onChange={ ( value ) =>
											updateWarranty( {
												length: value as Warranty[ 'length' ],
											} )
										}
									/>
									{ warranty.length === 'limited' && (
										<div>
											<TextControl
												label={ __(
													'Warranty Duration Value',
													'woocommerce-warranty'
												) }
												type="number"
												value={ warranty.value }
												onChange={ ( value ) =>
													updateWarranty( { value } )
												}
											/>
											<SelectControl
												label={ __(
													'Warranty Duration',
													'woocommerce-warranty'
												) }
												value={ warranty.duration }
												options={ [
													{
														label: __(
															'Days',
															'woocommerce-warranty'
														),
														value: 'days',
													},
													{
														label: __(
															'Weeks',
															'woocommerce-warranty'
														),
														value: 'weeks',
													},
													{
														label: __(
															'Months',
															'woocommerce-warranty'
														),
														value: 'months',
													},
													{
														label: __(
															'Years',
															'woocommerce-warranty'
														),
														value: 'years',
													},
												] }
												onChange={ ( value ) =>
													updateWarranty( {
														duration: value
															? ( value as WarrantyDuration )
															: 'days',
													} )
												}
											/>
										</div>
									) }
								</div>
							) }
							<hr />
							{ warranty.type === 'addon_warranty' && (
								<div>
									<CheckboxControl
										label={ __(
											'"No Warranty" Option',
											'woocommerce-warranty'
										) }
										checked={
											warranty.no_warranty_option ===
											'yes'
										}
										onChange={ ( isChecked ) =>
											updateWarranty( {
												no_warranty_option: isChecked
													? 'yes'
													: 'no',
											} )
										}
									/>
									{ /* Addons Handling */ }
									{ warranty.addons.map(
										(
											addon: WarrantyAddon,
											index: number
										) => (
											<div
												key={ index }
												style={ {
													border: '1px solid #ccc',
													padding: '10px',
													marginBottom: '10px',
												} }
											>
												<TextControl
													label={ __(
														'Amount',
														'woocommerce-warranty'
													) }
													type="number"
													value={
														addon.amount !==
														undefined
															? addon.amount.toString()
															: ''
													}
													onChange={ ( value ) => {
														const newAmount =
															parseFloat(
																value
															) || 0;
														const newAddons =
															warranty.addons.map(
																(
																	addonItem,
																	idx
																) =>
																	idx ===
																	index
																		? {
																				...addonItem,
																				amount: newAmount,
																		  }
																		: addonItem
															);
														updateWarranty( {
															addons: newAddons,
														} );
													} }
												/>
												<TextControl
													label={ __(
														'Value',
														'woocommerce-warranty'
													) }
													value={ addon.value }
													onChange={ ( value ) => {
														const newAddons =
															warranty.addons.map(
																(
																	addonItem,
																	idx
																) =>
																	idx ===
																	index
																		? {
																				...addonItem,
																				value,
																		  }
																		: addonItem
															);
														updateWarranty( {
															addons: newAddons,
														} );
													} }
												/>
												<SelectControl
													label={ __(
														'Duration',
														'woocommerce-warranty'
													) }
													value={ addon.duration }
													options={ [
														{
															label: __(
																'Days',
																'woocommerce-warranty'
															),
															value: 'days',
														},
														{
															label: __(
																'Weeks',
																'woocommerce-warranty'
															),
															value: 'weeks',
														},
														{
															label: __(
																'Months',
																'woocommerce-warranty'
															),
															value: 'months',
														},
														{
															label: __(
																'Years',
																'woocommerce-warranty'
															),
															value: 'years',
														},
													] }
													onChange={ (
														value: WarrantyDuration
													) => {
														const newAddons =
															warranty.addons.map(
																(
																	addonItem,
																	idx
																) =>
																	idx ===
																	index
																		? {
																				...addonItem,
																				duration:
																					value
																						? ( value as WarrantyDuration )
																						: 'days',
																		  }
																		: addonItem
															);
														updateWarranty( {
															addons: newAddons,
														} );
													} }
												/>
												<Button
													isDestructive
													onClick={ () => {
														const newAddons =
															warranty.addons.filter(
																( _, idx ) =>
																	idx !==
																	index
															);
														updateWarranty( {
															addons: newAddons,
														} );
													} }
												>
													{ __(
														'Remove Add-On',
														'woocommerce-warranty'
													) }
												</Button>
											</div>
										)
									) }
									<Button
										variant="secondary"
										onClick={ () => {
											const newAddon: WarrantyAddon = {
												amount: 0,
												value: '',
												duration: 'days',
											};
											const newAddons = [
												...warranty.addons,
												newAddon,
											];
											updateWarranty( {
												addons: newAddons,
											} );
										} }
									>
										{ __(
											'Add Add-On',
											'woocommerce-warranty'
										) }
									</Button>
								</div>
							) }
						</div>
					) }
				</div>
			) }
		</div>
	);
}
