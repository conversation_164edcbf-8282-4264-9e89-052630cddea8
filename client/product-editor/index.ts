/**
 * External dependencies
 */
// @ts-ignore
import { registerProductEditorBlockType } from '@woocommerce/product-editor';

import './editor.scss';

/**
 * Internal dependencies
 */
import { Edit } from './edit';
import blockConfiguration from './block.json';

const { name, ...metadata } = blockConfiguration;

export { metadata, name };

export const settings = {
	edit: Edit,
};

/**
 * Every block starts by registering a new block type definition.
 *
 * @see https://developer.wordpress.org/block-editor/developers/block-api/#registering-a-block
 */
registerProductEditorBlockType( {
	name,
	metadata: metadata as never,
	settings: settings as never,
} );
