{"name": "woocommerce-warranty", "title": "WooCommerce Warranty", "description": "Set warranties for your products (free and paid), and allow customers to purchase warranties when buying a product, and to initiate a return request right from their account. Manage RMA numbers, return status, email communications, and track return shipping easily with this extension.", "version": "2.6.8", "homepage": "https://woocommerce.com/products/warranty-requests/", "license": "GPL-3.0", "repository": {"type": "git", "url": "git://github.com/woocommerce/woocommerce-warranty.git"}, "devDependencies": {"@types/wordpress__block-editor": "^7.0.0", "@types/wordpress__blocks": "^11.0.9", "@typescript-eslint/eslint-plugin": "5.30.5", "@woocommerce/block-templates": "^1.1.0", "@woocommerce/components": "^12.3.0", "@woocommerce/dependency-extraction-webpack-plugin": "^3.0.1", "@woocommerce/eslint-plugin": "^2.3.0", "@woocommerce/navigation": "^8.2.0", "@woocommerce/product-editor": "^1.5.0", "@wordpress/prettier-config": "^4.8.0", "@wordpress/scripts": "^27.9.0", "@wordpress/stylelint-config": "^22.4.0", "ajv": "^7.2.4", "clean-css-cli": "^4.3.0", "eslint": "^8.56.0", "eslint-import-resolver-typescript": "3.2.4", "eslint-import-resolver-webpack": "^0.13.8", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "install": "^0.13.0", "node-wp-i18n": "~1.2.3", "sass": "^1.77.5", "typescript": "^5.2.0", "uglify-js": "^3.6.0"}, "config": {"use_pnpm": true, "translate": true, "use_gh_release_notes": true, "paths": {"js": "assets/js/*.js", "js_min": "assets/js/*.min.js", "css": "assets/css/*.css", "sass": "assets/css", "cssfolder": "assets/css"}}, "scripts": {"start": "wp-scripts start --webpack-src-dir=./client", "build": "pnpm run build:prod && pnpm run archive", "build:dev": "wp-scripts build --webpack-src-dir=./client && composer install && pnpm run makepot && pnpm run sass", "build:prod": "wp-scripts build --webpack-src-dir=./client && composer install -o --no-dev && pnpm run makepot && pnpm run sass", "archive": "composer archive --file=$npm_package_name --format=zip", "postarchive": "rm -rf $npm_package_name && unzip $npm_package_name.zip -d $npm_package_name && rm $npm_package_name.zip && zip -r $npm_package_name.zip $npm_package_name && rm -rf $npm_package_name", "makepot": "wpi18n makepot --domain-path languages --pot-file woocommerce-warranty.pot --type plugin --main-file woocommerce-warranty.php --exclude node_modules,tests,docs", "preuglify": "rm -f $npm_package_config_paths_js_min", "uglify": "for f in $npm_package_config_paths_js; do file=${f%.js}; node_modules/.bin/uglifyjs $f -c -m > $file.min.js; done", "presass": "rm -f $npm_package_config_paths_css", "test": "$npm_package_config_paths_css", "sass": "sass $npm_package_config_paths_sass:$npm_package_config_paths_cssfolder --no-source-map --style compressed", "watchsass": "sass $npm_package_config_paths_sass:$npm_package_config_paths_cssfolder --watch", "postsass": "for f in $npm_package_config_paths_css; do echo Processing $f; file=${f%.css}; node_modules/.bin/cleancss -o $file.css $f; done", "lint": "wp-scripts lint-js ./client", "lint:fix": "wp-scripts lint-js ./client --fix", "build:qit": "pnpm run build:prod && pnpm run archive:qit && pnpm run postarchive", "archive:qit": "composer archive --file=$npm_package_name --format=zip && pnpm run zip:phpstan_config", "zip:phpstan_config": "zip -r $npm_package_name.zip .phpstan/dist/* -j"}, "engines": {"node": "^22.14.0", "pnpm": "^10.4.1"}}