[![CI](https://github.com/woocommerce/woocommerce-warranty/actions/workflows/merge_to_trunk.yml/badge.svg)](https://github.com/woocommerce/woocommerce-warranty/actions/workflows/merge_to_trunk.yml)
[![CI](https://github.com/woocommerce/woocommerce-warranty/actions/workflows/cron_qit.yml/badge.svg)](https://github.com/woocommerce/woocommerce-warranty/actions/workflows/cron_qit.yml)

# Returns and Warranty Requests

Set warranties for your products (free and paid), and allow customers to purchase warranties when buying a product, and to initiate a return request right from their account. Manage RMA numbers, return status, email communications, and track return shipping easily with this extension.

| Product Page | Documentation | Ideas board |
| ------------ | ------------- | ----------- |
| https://woocommerce.com/products/warranty-requests/ | https://woocommerce.com/document/warranty-and-returns/ | https://woocommerce.com/feature-requests/warranty-requests |

## NPM Scripts

WooCommerce Warranty utilizes npm scripts for task management utilities.

`pnpm run build` - Runs the tasks necessary for a release. These may include building JavaScript, SASS, CSS minification, and language files.
