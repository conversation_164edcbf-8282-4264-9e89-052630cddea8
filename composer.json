{"name": "woocommerce/woocommerce-warranty", "description": "Set warranties for your products (free and paid), and allow customers to purchase warranties when buying a product, and to initiate a return request right from their account. Manage RMA numbers, return status, email communications, and track return shipping easily with this extension.", "homepage": "https://woocommerce.com/products/warranty-requests/", "type": "wordpress-plugin", "license": "GPL-2.0+", "archive": {"exclude": ["!/assets", "!/languages", "/node_modules", "bin", "tests", "README.md", "DEVELOPER.md", "package.json", "phpunit.xml.dist", "webpack.config.js", "postcss.config.js", "phpcs.xml", "package-lock.json", "composer.json", "composer.lock", "babel.config.js", "phpunit.xml", "jest-puppeteer.config.js", ".*", "woocommerce-warranty.zip", "/vendor", ".phpcs.security.xml", "client", "pnpm-lock.yaml", "tsconfig.json"]}, "require-dev": {"10up/wp_mock": "dev-trunk", "woocommerce/qit-cli": "*", "squizlabs/php_codesniffer": "*", "dealerdirect/phpcodesniffer-composer-installer": "*", "wp-coding-standards/wpcs": "*", "woocommerce/woocommerce-sniffs": "*", "phpstan/phpstan": "^2", "szepeviktor/phpstan-wordpress": "^2", "php-stubs/wp-cli-stubs": "*", "lucasbustamante/stubz": "^0"}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "scripts": {"phpstan": ["php -d memory_limit=2G ./vendor/bin/phpstan analyse --configuration=.phpstan/local-config.neon --level=2"], "check-security": ["./vendor/bin/phpcs . --ignore=vendor,.git,.phpstan,assets,node_modules,dist --standard=./.phpcs.security.xml  --report-full --report-summary"], "check-php": ["./vendor/bin/phpcs . --ignore=vendor,.git,.phpstan,assets,node_modules,dist --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra  --report-full --report-summary --colors"], "check-php:fix": ["./vendor/bin/phpcbf . --ignore=vendor,.git,.phpstan,assets,node_modules,dist --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra  --report-full --report-summary --colors"], "check-all": ["./vendor/bin/phpcs . --ignore=vendor,.git,.phpstan,assets,node_modules,dist --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra,WordPress-Docs  --report-full --report-summary --colors -s"], "check-all:fix": ["./vendor/bin/phpcbf . --ignore=vendor,.git,.phpstan,assets,node_modules,dist --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra,WordPress-Docs  --report-full --report-summary --colors"], "qit:security": ["npm run build && ./vendor/bin/qit run:security woocommerce-warranty --zip=woocommerce-warranty.zip"]}}