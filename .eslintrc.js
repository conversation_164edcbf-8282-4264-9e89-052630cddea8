module.exports = {
	extends: [ 'plugin:@woocommerce/eslint-plugin/recommended' ],
	rules: {
		'react/react-in-jsx-scope': 'off',
		'react/jsx-uses-react': 'off',
		'jest/no-deprecated-functions': 'off',
		'@woocommerce/dependency-group': 'off',
		'@wordpress/no-unsafe-wp-apis': 'off',
		'@typescript-eslint/ban-ts-comment': 'off',
		'jsdoc/newline-after-description': 'off',
		'@typescript-eslint/no-duplicate-imports': 'off',
		'@wordpress/i18n-text-domain': [
			'error',
			{
				allowedTextDomain: 'woocommerce-warranty',
			},
		],
	},
};
