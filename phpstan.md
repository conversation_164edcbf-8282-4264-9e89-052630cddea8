composer run-script phpstan
> php -d memory_limit=2G ./vendor/bin/phpstan analyse --configuration=.phpstan/local-config.neon --level=2
0/57 [░░░░░░░░░░░░░░░░░░░░░░░░░░░░]   0% 20/57 [▓▓▓▓▓▓▓▓▓░░░░░░░░░░░░░░░░░░░]  35% 40/57 [▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓░░░░░░░░░]  70% 57/57 [▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓] 100%

 ------ ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- 
Line   includes/class-product-editor.php
 ------ ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- 
58     <USER> <GROUP> method register_block_type_from_metadata() on an unknown class Automattic\WooCommerce\Admin\Features\ProductBlockEditor\Automattic\WooCommerce\Admin\Features\ProductBlockEditor\BlockRegistry.               
🪪  class.notFound                                                                                                                                                                                                     
💡  Learn more at https://phpstan.org/user-guide/discovering-symbols                                                                                                                                                   
71     Call to method add_group() on an unknown class Automattic\WooCommerce\Admin\BlockTemplates\Automattic\WooCommerce\Admin\BlockTemplates\ContainerInterface.                                                            
🪪  class.notFound                                                                                                                                                                                                     
💡  Learn more at https://phpstan.org/user-guide/discovering-symbols                                                                                                                                                   
101    Call to method add_block() on an unknown class Automattic\WooCommerce\Admin\Features\ProductBlockEditor\ProductTemplates\Automattic\WooCommerce\Admin\Features\ProductBlockEditor\ProductTemplates\SectionInterface.  
🪪  class.notFound                                                                                                                                                                                                     
💡  Learn more at https://phpstan.org/user-guide/discovering-symbols
 ------ ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- 

 ------ -------------------------------------------------------------------------------------------------------------------- 
Line   includes/class-warranty-active-reports-list-table.php
 ------ -------------------------------------------------------------------------------------------------------------------- 
13     <USER> <GROUP> require_once() "./wp-admin/includes/class-wp-list-table.php" is not a file or it does not exist.            
🪪  requireOnce.fileNotFound                                                                                         
212    Method Warranty_Active_Reports_List_Table::column_validity() should return string but return statement is missing.  
🪪  return.missing
 ------ -------------------------------------------------------------------------------------------------------------------- 

 ------ ---------------------------------------------------------------------------------------------------------------------------- 
Line   includes/class-warranty-admin.php
 ------ ---------------------------------------------------------------------------------------------------------------------------- 
54     <USER> <GROUP> returns void|false but should not return anything.                                                          
🪪  return.void                                                                                                              
496    Call to an undefined method WooCommerce::add_inline_js().                                                                   
🪪  method.notFound                                                                                                          
725    Constant WC_PRODUCT_VENDORS_COMMISSION_TABLE not found.                                                                     
🪪  constant.notFound                                                                                                        
💡  Learn more at https://phpstan.org/user-guide/discovering-symbols                                                         
738    Constant WC_PRODUCT_VENDORS_COMMISSION_TABLE not found.                                                                     
🪪  constant.notFound                                                                                                        
💡  Learn more at https://phpstan.org/user-guide/discovering-symbols                                                         
769    Constant WC_PRODUCT_VENDORS_COMMISSION_TABLE not found.                                                                     
🪪  constant.notFound                                                                                                        
💡  Learn more at https://phpstan.org/user-guide/discovering-symbols                                                         
782    Constant WC_PRODUCT_VENDORS_COMMISSION_TABLE not found.                                                                     
🪪  constant.notFound                                                                                                        
💡  Learn more at https://phpstan.org/user-guide/discovering-symbols                                                         
813    Constant WC_PRODUCT_VENDORS_COMMISSION_TABLE not found.                                                                     
🪪  constant.notFound                                                                                                        
💡  Learn more at https://phpstan.org/user-guide/discovering-symbols                                                         
825    Constant WC_PRODUCT_VENDORS_COMMISSION_TABLE not found.                                                                     
🪪  constant.notFound                                                                                                        
💡  Learn more at https://phpstan.org/user-guide/discovering-symbols                                                         
860    Constant WC_PRODUCT_VENDORS_COMMISSION_TABLE not found.                                                                     
🪪  constant.notFound                                                                                                        
💡  Learn more at https://phpstan.org/user-guide/discovering-symbols                                                         
872    Constant WC_PRODUCT_VENDORS_COMMISSION_TABLE not found.                                                                     
🪪  constant.notFound                                                                                                        
💡  Learn more at https://phpstan.org/user-guide/discovering-symbols                                                         
887    PHPDoc tag @return has invalid value (Array.): Unexpected token ".", expected TOKEN_HORIZONTAL_WS at offset 165 on line 7   
🪪  phpDoc.parseError                                                                                                        
1050   Variable $idx might not be defined.                                                                                         
🪪  variable.undefined                                                                                                       
1133   Variable $message might not be defined.                                                                                     
🪪  variable.undefined                                                                                                       
1568   PHPDoc tag @return has invalid value (String.): Unexpected token ".", expected TOKEN_HORIZONTAL_WS at offset 110 on line 6  
🪪  phpDoc.parseError                                                                                                        
1726   Access to an undefined property Warranty_Admin::$row_reason_injected.                                                       
🪪  property.notFound                                                                                                        
💡  Learn more: https://phpstan.org/blog/solving-phpstan-access-to-undefined-property                                        
1726   Variable $item in empty() is never defined.                                                                                 
🪪  empty.variable                                                                                                           
1727   Undefined variable: $item                                                                                                   
🪪  variable.undefined
 ------ ---------------------------------------------------------------------------------------------------------------------------- 

 ------ -------------------------------------------------------------------------------------------------------------------------------------------------------------------- 
Line   includes/class-warranty-ajax.php
 ------ -------------------------------------------------------------------------------------------------------------------------------------------------------------------- 
112    <USER> <GROUP> @param has invalid value (boolean Whether the product is from the vendor or not.): Unexpected token "Whether", expected variable at offset 72 on line 4  
🪪  phpDoc.parseError                                                                                                                                                
116    One or more @param tags has an invalid name or invalid syntax.                                                                                                      
🪪  phpDoc.parseError                                                                                                                                                
152    PHPDoc tag @param has invalid value (array All users from user search results.): Unexpected token "All", expected variable at offset 77 on line 4                   
🪪  phpDoc.parseError                                                                                                                                                
153    PHPDoc tag @param has invalid value (string|boolean Term value.): Unexpected token "Term", expected variable at offset 140 on line 5                                
🪪  phpDoc.parseError                                                                                                                                                
157    One or more @param tags has an invalid name or invalid syntax.                                                                                                      
🪪  phpDoc.parseError                                                                                                                                                
212    PHPDoc tag @return has invalid value (array.): Unexpected token ".", expected TOKEN_HORIZONTAL_WS at offset 85 on line 4                                            
🪪  phpDoc.parseError                                                                                                                                                
333    PHPDoc tag @param has invalid value (array Email search results.): Unexpected token "Email", expected variable at offset 74 on line 4                               
🪪  phpDoc.parseError                                                                                                                                                
334    PHPDoc tag @param has invalid value (string|boolean Term value.): Unexpected token "Term", expected variable at offset 123 on line 5                                
🪪  phpDoc.parseError                                                                                                                                                
335    PHPDoc tag @param has invalid value (array All emails.): Unexpected token "All", expected variable at offset 153 on line 6                                          
🪪  phpDoc.parseError                                                                                                                                                
339    One or more @param tags has an invalid name or invalid syntax.                                                                                                      
🪪  phpDoc.parseError
 ------ -------------------------------------------------------------------------------------------------------------------------------------------------------------------- 

 ------ ------------------------------------------------------------------------------------------------------------------------------------------ 
Line   includes/class-warranty-cart.php
 ------ ------------------------------------------------------------------------------------------------------------------------------------------ 
170    <USER> <GROUP> of the parameter #1 $valid (string) of method Warranty_Cart::add_cart_validation() is incompatible with type bool.          
🪪  parameter.defaultValue                                                                                                                 
170    Default value of the parameter #2 $product_id (string) of method Warranty_Cart::add_cart_validation() is incompatible with type int.      
🪪  parameter.defaultValue                                                                                                                 
170    Default value of the parameter #4 $variation_id (string) of method Warranty_Cart::add_cart_validation() is incompatible with type int.    
🪪  parameter.defaultValue                                                                                                                 
229    PHPDoc tag @param has invalid value (string Add ons combo box text.): Unexpected token "Add", expected variable at offset 77 on line 4    
🪪  phpDoc.parseError                                                                                                                      
233    One or more @param tags has an invalid name or invalid syntax.                                                                            
🪪  phpDoc.parseError                                                                                                                      
279    PHPDoc tag @param has invalid value (array URL link from product ID.): Unexpected token "URL", expected variable at offset 68 on line 4   
🪪  phpDoc.parseError                                                                                                                      
283    One or more @param tags has an invalid name or invalid syntax.                                                                            
🪪  phpDoc.parseError                                                                                                                      
287    PHPDoc tag @param has invalid value (string URL link from product ID.): Unexpected token "URL", expected variable at offset 66 on line 4  
🪪  phpDoc.parseError                                                                                                                      
291    One or more @param tags has an invalid name or invalid syntax.                                                                            
🪪  phpDoc.parseError                                                                                                                      
306    Access to an undefined property WC_Product::$parent.                                                                                      
🪪  property.notFound                                                                                                                      
💡  Learn more: https://phpstan.org/blog/solving-phpstan-access-to-undefined-property
 ------ ------------------------------------------------------------------------------------------------------------------------------------------ 

 ------ ---------------------------------------------------------------------------------------------------------------------------- 
Line   includes/class-warranty-completed-reports-list-table.php
 ------ ---------------------------------------------------------------------------------------------------------------------------- 
13     <USER> <GROUP> require_once() "./wp-admin/includes/class-wp-list-table.php" is not a file or it does not exist.                    
🪪  requireOnce.fileNotFound                                                                                                 
126    PHPDoc tag @return has invalid value (string.): Unexpected token ".", expected TOKEN_HORIZONTAL_WS at offset 103 on line 6  
🪪  phpDoc.parseError                                                                                                        
146    PHPDoc tag @return has invalid value (string.): Unexpected token ".", expected TOKEN_HORIZONTAL_WS at offset 101 on line 6  
🪪  phpDoc.parseError                                                                                                        
160    PHPDoc tag @return has invalid value (string.): Unexpected token ".", expected TOKEN_HORIZONTAL_WS at offset 103 on line 6  
🪪  phpDoc.parseError                                                                                                        
176    PHPDoc tag @return has invalid value (string.): Unexpected token ".", expected TOKEN_HORIZONTAL_WS at offset 102 on line 6  
🪪  phpDoc.parseError                                                                                                        
204    PHPDoc tag @return has invalid value (string.): Unexpected token ".", expected TOKEN_HORIZONTAL_WS at offset 103 on line 6  
🪪  phpDoc.parseError                                                                                                        
249    PHPDoc tag @return has invalid value (string.): Unexpected token ".", expected TOKEN_HORIZONTAL_WS at offset 99 on line 6   
🪪  phpDoc.parseError
 ------ ---------------------------------------------------------------------------------------------------------------------------- 

 ------ ---------------------------------------------------------------------------------------------------------------------------- 
Line   includes/class-warranty-frontend.php
 ------ ---------------------------------------------------------------------------------------------------------------------------- 
182    <USER> <GROUP> referenced with incorrect case: WC_order.                                                                    
🪪  class.nameCase                                                                                                           
344    Variable $idxs in empty() always exists and is not falsy.                                                                   
🪪  empty.variable                                                                                                           
613    PHPDoc tag @return has invalid value (String.): Unexpected token ".", expected TOKEN_HORIZONTAL_WS at offset 156 on line 6  
🪪  phpDoc.parseError
 ------ ---------------------------------------------------------------------------------------------------------------------------- 

 ------ ---------------------------------------------------------------------------------------------- 
Line   includes/class-warranty-install.php
 ------ ---------------------------------------------------------------------------------------------- 
359    <USER> <GROUP> require_once() "./wp-admin/includes/upgrade.php" is not a file or it does not exist.  
🪪  requireOnce.fileNotFound                                                                   
406    Cannot access property $slug on false.                                                        
🪪  property.nonObject
 ------ ---------------------------------------------------------------------------------------------- 

 ------ ---------------------------------------------------------------------------------------------------------- 
Line   includes/class-warranty-list-table.php
 ------ ---------------------------------------------------------------------------------------------------------- 
13     <USER> <GROUP> require_once() "./wp-admin/includes/class-wp-list-table.php" is not a file or it does not exist.  
🪪  requireOnce.fileNotFound
 ------ ---------------------------------------------------------------------------------------------------------- 

 ------ -------------------------------------------------------------------------------------------------------------------------------------------------------- 
Line   includes/class-warranty-order.php
 ------ -------------------------------------------------------------------------------------------------------------------------------------------------------- 
220    <USER> <GROUP> @param has invalid value (boolean Whether order has warranty or not.): Unexpected token "Whether", expected variable at offset 84 on line 4  
🪪  phpDoc.parseError                                                                                                                                    
221    PHPDoc tag @param has invalid value (WC_Order Order object.): Unexpected token "Order", expected variable at offset 140 on line 5                       
🪪  phpDoc.parseError                                                                                                                                    
225    One or more @param tags has an invalid name or invalid syntax.                                                                                          
🪪  phpDoc.parseError
 ------ -------------------------------------------------------------------------------------------------------------------------------------------------------- 

 ------ ----------------------------------------------------------- 
Line   includes/class-warranty-settings.php
 ------ ----------------------------------------------------------- 
1100   <USER> <GROUP> an undefined method WooCommerce::add_inline_js().  
🪪  method.notFound
 ------ ----------------------------------------------------------- 

 ------ ------------------------------------------------------------------------------------------------------------------------------------------- 
Line   includes/class-woocommerce-warranty.php
 ------ ------------------------------------------------------------------------------------------------------------------------------------------- 
161    <USER> <GROUP> @param has invalid value (array List of subfolders.): Unexpected token "List", expected variable at offset 94 on line 4         
🪪  phpDoc.parseError                                                                                                                       
165    One or more @param tags has an invalid name or invalid syntax.                                                                             
🪪  phpDoc.parseError                                                                                                                       
242    PHPDoc tag @param has invalid value (array List of form builder tips.): Unexpected token "List", expected variable at offset 80 on line 4  
🪪  phpDoc.parseError                                                                                                                       
246    One or more @param tags has an invalid name or invalid syntax.                                                                             
🪪  phpDoc.parseError                                                                                                                       
338    PHPDoc tag @param has invalid value (array List of warranty statuses.): Unexpected token "List", expected variable at offset 80 on line 4  
🪪  phpDoc.parseError                                                                                                                       
342    One or more @param tags has an invalid name or invalid syntax.                                                                             
🪪  phpDoc.parseError
 ------ ------------------------------------------------------------------------------------------------------------------------------------------- 

 ------ ----------------------------------------------------------------------------------------------------------------------------------------------- 
Line   includes/functions.php
 ------ ----------------------------------------------------------------------------------------------------------------------------------------------- 
55     <USER> <GROUP> more @param tags has an invalid name or invalid syntax.                                                                                 
🪪  phpDoc.parseError                                                                                                                           
68     Variable $meta_list in empty() always exists and is not falsy.                                                                                 
🪪  empty.variable                                                                                                                              
296    Variable $warranty_string might not be defined.                                                                                                
🪪  variable.undefined                                                                                                                          
354    PHPDoc tag @param has invalid value (array Warranty data.): Unexpected token "Warranty", expected variable at offset 80 on line 4              
🪪  phpDoc.parseError                                                                                                                           
355    PHPDoc tag @param has invalid value (int Product ID.): Unexpected token "Product", expected variable at offset 110 on line 5                   
🪪  phpDoc.parseError                                                                                                                           
359    One or more @param tags has an invalid name or invalid syntax.                                                                                 
🪪  phpDoc.parseError                                                                                                                           
425    PHPDoc tag @param has invalid value (string Warranty text.): Unexpected token "Warranty", expected variable at offset 91 on line 4             
🪪  phpDoc.parseError                                                                                                                           
426    PHPDoc tag @param has invalid value (int Product ID.): Unexpected token "Product", expected variable at offset 121 on line 5                   
🪪  phpDoc.parseError                                                                                                                           
427    PHPDoc tag @param has invalid value (array Warranty Data.): Unexpected token "Warranty", expected variable at offset 150 on line 6             
🪪  phpDoc.parseError                                                                                                                           
431    One or more @param tags has an invalid name or invalid syntax.                                                                                 
🪪  phpDoc.parseError                                                                                                                           
494    PHPDoc tag @param has invalid value (array Value of warranty settings.): Unexpected token "Value", expected variable at offset 78 on line 4    
🪪  phpDoc.parseError                                                                                                                           
498    One or more @param tags has an invalid name or invalid syntax.                                                                                 
🪪  phpDoc.parseError                                                                                                                           
523    Method WC_Logger::add() invoked with 2 parameters, 3 required.                                                                                 
🪪  arguments.count                                                                                                                             
537    PHPDoc tag @param has invalid value (float Total amount of order.): Unexpected token "Total", expected variable at offset 95 on line 4         
🪪  phpDoc.parseError                                                                                                                           
538    PHPDoc tag @param has invalid value (int Warranty request ID.): Unexpected token "Warranty", expected variable at offset 133 on line 5         
🪪  phpDoc.parseError                                                                                                                           
542    One or more @param tags has an invalid name or invalid syntax.                                                                                 
🪪  phpDoc.parseError                                                                                                                           
589    Method WC_Logger::add() invoked with 2 parameters, 3 required.                                                                                 
🪪  arguments.count                                                                                                                             
603    PHPDoc tag @param has invalid value (float Total amount of request item.): Unexpected token "Total", expected variable at offset 80 on line 4  
🪪  phpDoc.parseError                                                                                                                           
604    PHPDoc tag @param has invalid value (int Warranty request ID.): Unexpected token "Warranty", expected variable at offset 125 on line 5         
🪪  phpDoc.parseError                                                                                                                           
608    One or more @param tags has an invalid name or invalid syntax.                                                                                 
🪪  phpDoc.parseError                                                                                                                           
766    PHPDoc tag @param has invalid value (array Warranty status terms.): Unexpected token "Warranty", expected variable at offset 77 on line 4      
🪪  phpDoc.parseError                                                                                                                           
770    One or more @param tags has an invalid name or invalid syntax.                                                                                 
🪪  phpDoc.parseError                                                                                                                           
797    Cannot access property $slug on false.                                                                                                         
🪪  property.nonObject                                                                                                                          
830    One or more @param tags has an invalid name or invalid syntax.                                                                                 
🪪  phpDoc.parseError                                                                                                                           
881    Binary operation "+" between string and int results in an error.                                                                               
🪪  binaryOp.invalid                                                                                                                            
882    Binary operation "+" between string and (float|int) results in an error.                                                                       
🪪  binaryOp.invalid                                                                                                                            
895    Binary operation "+" between string and int results in an error.                                                                               
🪪  binaryOp.invalid                                                                                                                            
900    Binary operation "-" between string and 1 results in an error.                                                                                 
🪪  binaryOp.invalid                                                                                                                            
1029   PHPDoc tag @param has invalid value (array Warranty post data.): Unexpected token "Warranty", expected variable at offset 97 on line 4         
🪪  phpDoc.parseError                                                                                                                           
1033   Expected 2 @param tags, found 1.                                                                                                               
🪪  paramTag.count                                                                                                                              
1118   One or more @param tags has an invalid name or invalid syntax.                                                                                 
🪪  phpDoc.parseError                                                                                                                           
1247   PHPDoc tag @param has invalid value (array Warranty data.): Unexpected token "Warranty", expected variable at offset 65 on line 4              
🪪  phpDoc.parseError                                                                                                                           
1252   One or more @param tags has an invalid name or invalid syntax.                                                                                 
🪪  phpDoc.parseError                                                                                                                           
1264   Class WP_User referenced with incorrect case: WP_user.                                                                                         
🪪  class.nameCase                                                                                                                              
1286   Call to static method can_vendor_access_order() on an unknown class WC_Product_Vendors_Utils.                                                  
🪪  class.notFound                                                                                                                              
💡  Learn more at https://phpstan.org/user-guide/discovering-symbols                                                                            
1286   Call to static method get_user_active_vendor() on an unknown class WC_Product_Vendors_Utils.                                                   
🪪  class.notFound                                                                                                                              
💡  Learn more at https://phpstan.org/user-guide/discovering-symbols                                                                            
1546   Access to an undefined property WooCommerce::$payment_gateways.                                                                                
🪪  property.notFound                                                                                                                           
💡  Learn more: https://phpstan.org/blog/solving-phpstan-access-to-undefined-property                                                           
1984   PHPDoc tag @return has invalid value (Array.): Unexpected token ".", expected TOKEN_HORIZONTAL_WS at offset 83 on line 6                       
🪪  phpDoc.parseError                                                                                                                           
2060   PHPDoc tag @return has invalid value (WC_Order.): Unexpected token ".", expected TOKEN_HORIZONTAL_WS at offset 125 on line 6                   
🪪  phpDoc.parseError
 ------ ----------------------------------------------------------------------------------------------------------------------------------------------- 

 ------ --------------------------------------------------------------------------------------------------------------------------- 
Line   includes/trait-warranty-util.php (in context of class Warranty_Admin)
 ------ --------------------------------------------------------------------------------------------------------------------------- 
147    <USER> <GROUP> @return has invalid value (mixed.): Unexpected token ".", expected TOKEN_HORIZONTAL_WS at offset 144 on line 7  
🪪  phpDoc.parseError                                                                                                       
160    PHPDoc tag @return has invalid value (mixed.): Unexpected token ".", expected TOKEN_HORIZONTAL_WS at offset 181 on line 7  
🪪  phpDoc.parseError
 ------ --------------------------------------------------------------------------------------------------------------------------- 

 ------ --------------------------------------------------------------------------------------------------------------------------- 
Line   includes/trait-warranty-util.php (in context of class Warranty_Ajax)
 ------ --------------------------------------------------------------------------------------------------------------------------- 
147    <USER> <GROUP> @return has invalid value (mixed.): Unexpected token ".", expected TOKEN_HORIZONTAL_WS at offset 144 on line 7  
🪪  phpDoc.parseError                                                                                                       
160    PHPDoc tag @return has invalid value (mixed.): Unexpected token ".", expected TOKEN_HORIZONTAL_WS at offset 181 on line 7  
🪪  phpDoc.parseError
 ------ --------------------------------------------------------------------------------------------------------------------------- 

 ------ ----------------------------------------------------------------------------------------------------------------- 
Line   includes/updates/class-update-20221123.php
 ------ ----------------------------------------------------------------------------------------------------------------- 
174    <USER> <GROUP> require_once() "./wp-admin/includes/class-wp-filesystem-base.php" is not a file or it does not exist.    
🪪  requireOnce.fileNotFound                                                                                      
175    Path in require_once() "./wp-admin/includes/class-wp-filesystem-direct.php" is not a file or it does not exist.  
🪪  requireOnce.fileNotFound                                                                                      
233    Path in require_once() "./wp-admin/includes/class-wp-filesystem-base.php" is not a file or it does not exist.    
🪪  requireOnce.fileNotFound                                                                                      
234    Path in require_once() "./wp-admin/includes/class-wp-filesystem-direct.php" is not a file or it does not exist.  
🪪  requireOnce.fileNotFound
 ------ ----------------------------------------------------------------------------------------------------------------- 

 ------ ------------------------------------------------------------------------------------------------------ 
Line   templates/admin/new-warranty-form.php
 ------ ------------------------------------------------------------------------------------------------------ 
31     Variable $items in PHPDoc tag @var does not match any variable in the foreach loop: $idx_array, $idx  
🪪  varTag.differentVariable                                                                           
40     Variable $has_warranty might not be defined.                                                          
🪪  variable.undefined
 ------ ------------------------------------------------------------------------------------------------------ 

 ------ ------------------------------------------ 
Line   templates/admin/order-item-warranty.php
 ------ ------------------------------------------ 
17     Variable $order_id might not be defined.  
🪪  variable.undefined                     
22     Variable $item might not be defined.      
🪪  variable.undefined                     
29     Variable $item_id might not be defined.   
🪪  variable.undefined
 ------ ------------------------------------------ 

 ------ -------------------------------------------------- 
Line   templates/admin/product-panel.php
 ------ -------------------------------------------------- 
23     Variable $control_type might not be defined.      
🪪  variable.undefined                             
24     Variable $control_type might not be defined.      
🪪  variable.undefined                             
34     Variable $default_warranty might not be defined.  
🪪  variable.undefined                             
40     Variable $warranty might not be defined.          
🪪  variable.undefined                             
41     Variable $warranty might not be defined.          
🪪  variable.undefined                             
42     Variable $warranty might not be defined.          
🪪  variable.undefined                             
49     Variable $warranty_label might not be defined.    
🪪  variable.undefined                             
58     Variable $warranty might not be defined.          
🪪  variable.undefined                             
58     Variable $warranty might not be defined.          
🪪  variable.undefined                             
59     Variable $warranty might not be defined.          
🪪  variable.undefined                             
59     Variable $warranty might not be defined.          
🪪  variable.undefined                             
65     Variable $warranty might not be defined.          
🪪  variable.undefined                             
65     Variable $warranty might not be defined.          
🪪  variable.undefined                             
67     Variable $warranty might not be defined.          
🪪  variable.undefined                             
67     Variable $warranty might not be defined.          
🪪  variable.undefined                             
68     Variable $warranty might not be defined.          
🪪  variable.undefined                             
68     Variable $warranty might not be defined.          
🪪  variable.undefined                             
69     Variable $warranty might not be defined.          
🪪  variable.undefined                             
69     Variable $warranty might not be defined.          
🪪  variable.undefined                             
70     Variable $warranty might not be defined.          
🪪  variable.undefined                             
70     Variable $warranty might not be defined.          
🪪  variable.undefined                             
105    Variable $currency might not be defined.          
🪪  variable.undefined
 ------ -------------------------------------------------- 

 ------ ---------------------------------------------- 
Line   templates/list-item-details.php
 ------ ---------------------------------------------- 
11     Variable $request might not be defined.       
🪪  variable.undefined                         
12     Variable $this might not be defined.          
🪪  variable.undefined                         
28     Variable $request might not be defined.       
🪪  variable.undefined                         
32     Variable $request might not be defined.       
🪪  variable.undefined                         
32     Variable $request might not be defined.       
🪪  variable.undefined                         
51     Variable $request might not be defined.       
🪪  variable.undefined                         
85     Variable $request might not be defined.       
🪪  variable.undefined                         
92     Variable $request might not be defined.       
🪪  variable.undefined                         
93     Variable $request might not be defined.       
🪪  variable.undefined                         
94     Variable $request might not be defined.       
🪪  variable.undefined                         
94     Variable $request might not be defined.       
🪪  variable.undefined                         
95     Variable $request might not be defined.       
🪪  variable.undefined                         
108    Variable $request might not be defined.       
🪪  variable.undefined                         
117    Variable $request might not be defined.       
🪪  variable.undefined                         
181    Variable $request might not be defined.       
🪪  variable.undefined                         
182    Variable $request might not be defined.       
🪪  variable.undefined                         
182    Variable $request might not be defined.       
🪪  variable.undefined                         
192    Variable $request might not be defined.       
🪪  variable.undefined                         
193    Variable $request might not be defined.       
🪪  variable.undefined                         
193    Variable $update_nonce might not be defined.  
🪪  variable.undefined
 ------ ---------------------------------------------- 

 ------ -------------------------------------------- 
Line   templates/list-item-notes.php
 ------ -------------------------------------------- 
15     Variable $request_id might not be defined.  
🪪  variable.undefined                       
43     Variable $request_id might not be defined.  
🪪  variable.undefined
 ------ -------------------------------------------- 

 ------ ---------------------------------------------- 
Line   templates/list-item-refunds.php
 ------ ---------------------------------------------- 
11     Variable $request might not be defined.       
🪪  variable.undefined                         
12     Variable $request might not be defined.       
🪪  variable.undefined                         
16     Variable $refunded might not be defined.      
🪪  variable.undefined                         
20     Variable $item_amount might not be defined.   
🪪  variable.undefined                         
26     Variable $available might not be defined.     
🪪  variable.undefined                         
36     Variable $item_amount might not be defined.   
🪪  variable.undefined                         
37     Variable $refunded might not be defined.      
🪪  variable.undefined                         
38     Variable $request might not be defined.       
🪪  variable.undefined                         
39     Variable $update_nonce might not be defined.  
🪪  variable.undefined                         
43     Variable $request might not be defined.       
🪪  variable.undefined                         
45     Variable $request might not be defined.       
🪪  variable.undefined                         
46     Variable $request might not be defined.       
🪪  variable.undefined                         
48     Variable $request might not be defined.       
🪪  variable.undefined                         
60     Variable $refunded might not be defined.      
🪪  variable.undefined                         
64     Variable $item_amount might not be defined.   
🪪  variable.undefined                         
70     Variable $available might not be defined.     
🪪  variable.undefined                         
83     Variable $item_amount might not be defined.   
🪪  variable.undefined                         
84     Variable $refunded might not be defined.      
🪪  variable.undefined                         
85     Variable $request might not be defined.       
🪪  variable.undefined
 ------ ---------------------------------------------- 

 ------ ------------------------------------------- 
Line   templates/new.php
 ------ ------------------------------------------- 
19     Variable $form_view might not be defined.  
🪪  variable.undefined                      
19     Variable $searched might not be defined.   
🪪  variable.undefined                      
51     Variable $form_view might not be defined.  
🪪  variable.undefined                      
51     Variable $searched might not be defined.   
🪪  variable.undefined                      
55     Variable $searched might not be defined.   
🪪  variable.undefined
 ------ ------------------------------------------- 

 ------ ----------------------------------------------- 
Line   templates/print.php
 ------ ----------------------------------------------- 
17     Variable $warranty might not be defined.       
🪪  variable.undefined                          
69     Variable $logo might not be defined.           
🪪  variable.undefined                          
75     Variable $show_url might not be defined.       
🪪  variable.undefined                          
83     Variable $warranty might not be defined.       
🪪  variable.undefined                          
90     Variable $warranty might not be defined.       
🪪  variable.undefined                          
94     Variable $order might not be defined.          
🪪  variable.undefined                          
98     Variable $email might not be defined.          
🪪  variable.undefined                          
98     Variable $first_name might not be defined.     
🪪  variable.undefined                          
98     Variable $last_name might not be defined.      
🪪  variable.undefined                          
102    Variable $product_name might not be defined.   
🪪  variable.undefined                          
106    Variable $warranty might not be defined.       
🪪  variable.undefined                          
110    Variable $inputs might not be defined.         
🪪  variable.undefined                          
115    Variable $form might not be defined.           
🪪  variable.undefined                          
131    Variable $tracking_html might not be defined.  
🪪  variable.undefined
 ------ ----------------------------------------------- 

 ------ ------------------------------------------ 
Line   templates/settings/addons-table.php
 ------ ------------------------------------------ 
14     Variable $value might not be defined.     
🪪  variable.undefined                     
23     Variable $addons might not be defined.    
🪪  variable.undefined                     
26     Variable $currency might not be defined.  
🪪  variable.undefined                     
48     Variable $currency might not be defined.  
🪪  variable.undefined
 ------ ------------------------------------------ 

 ------ -------------------------------------------------- 
Line   templates/settings/categories-table.php
 ------ -------------------------------------------------- 
13     Variable $value might not be defined.             
🪪  variable.undefined                             
22     Variable $categories might not be defined.        
🪪  variable.undefined                             
27     Variable $default_warranty might not be defined.  
🪪  variable.undefined                             
141    Variable $currency might not be defined.          
🪪  variable.undefined                             
179    Variable $currency might not be defined.          
🪪  variable.undefined
 ------ -------------------------------------------------- 

 ------ ---------------------------------------- 
Line   templates/settings/form-builder.php
 ------ ---------------------------------------- 
16     Variable $inputs might not be defined.  
🪪  variable.undefined                   
23     Variable $form might not be defined.    
🪪  variable.undefined                   
27     Variable $form might not be defined.    
🪪  variable.undefined                   
31     Variable $types might not be defined.   
🪪  variable.undefined                   
44     Variable $types might not be defined.   
🪪  variable.undefined                   
101    Variable $types might not be defined.   
🪪  variable.undefined                   
108    Variable $form might not be defined.    
🪪  variable.undefined
 ------ ---------------------------------------- 

 ------ --------------------------------------------------- 
Line   templates/settings/logo-field.php
 ------ --------------------------------------------------- 
13     Variable $value might not be defined.              
🪪  variable.undefined                              
13     Variable $value might not be defined.              
🪪  variable.undefined                              
17     Variable $tooltip_html might not be defined.       
🪪  variable.undefined                              
20     Variable $value might not be defined.              
🪪  variable.undefined                              
22     Variable $value might not be defined.              
🪪  variable.undefined                              
23     Variable $value might not be defined.              
🪪  variable.undefined                              
24     Variable $type might not be defined.               
🪪  variable.undefined                              
25     Variable $value might not be defined.              
🪪  variable.undefined                              
26     Variable $option_value might not be defined.       
🪪  variable.undefined                              
27     Variable $value might not be defined.              
🪪  variable.undefined                              
28     Variable $value might not be defined.              
🪪  variable.undefined                              
32     Variable $custom_attributes might not be defined.  
🪪  variable.undefined                              
35     Variable $value might not be defined.              
🪪  variable.undefined                              
35     Variable $value might not be defined.              
🪪  variable.undefined                              
39     Variable $description might not be defined.        
🪪  variable.undefined
 ------ --------------------------------------------------- 

 ------ --------------------------------------------------- 
Line   templates/settings/multi-status-field.php
 ------ --------------------------------------------------- 
13     Variable $value might not be defined.              
🪪  variable.undefined                              
13     Variable $value might not be defined.              
🪪  variable.undefined                              
17     Variable $tip might not be defined.                
🪪  variable.undefined                              
20     Variable $value might not be defined.              
🪪  variable.undefined                              
22     Variable $value might not be defined.              
🪪  variable.undefined                              
23     Variable $value might not be defined.              
🪪  variable.undefined                              
24     Variable $value might not be defined.              
🪪  variable.undefined                              
25     Variable $value might not be defined.              
🪪  variable.undefined                              
30     Variable $custom_attributes might not be defined.  
🪪  variable.undefined                              
34     Variable $value might not be defined.              
🪪  variable.undefined                              
38     Variable $option_value might not be defined.       
🪪  variable.undefined                              
48     Variable $description might not be defined.        
🪪  variable.undefined
 ------ --------------------------------------------------- 

 ------ ----------------------------------------------------- 
Line   templates/settings/permissions-table.php
 ------ ----------------------------------------------------- 
20     Variable $all_statuses might not be defined.         
🪪  variable.undefined                                
32     Variable $all_permitted_users might not be defined.  
🪪  variable.undefined
 ------ ----------------------------------------------------- 

 ------ ------------------------------------------ 
Line   templates/settings/settings-default.php
 ------ ------------------------------------------ 
13     Variable $settings might not be defined.  
🪪  variable.undefined
 ------ ------------------------------------------ 

 ------ ------------------------------------------ 
Line   templates/settings/settings-emails.php
 ------ ------------------------------------------ 
13     Variable $settings might not be defined.  
🪪  variable.undefined
 ------ ------------------------------------------ 

 ------ ------------------------------------------ 
Line   templates/settings/settings-form.php
 ------ ------------------------------------------ 
13     Variable $settings might not be defined.  
🪪  variable.undefined
 ------ ------------------------------------------ 

 ------ ------------------------------------------ 
Line   templates/settings/settings-general.php
 ------ ------------------------------------------ 
33     Variable $settings might not be defined.  
🪪  variable.undefined
 ------ ------------------------------------------ 

 ------ --------------------------------------------- 
Line   templates/settings/settings-permissions.php
 ------ --------------------------------------------- 
13     Variable $settings might not be defined.     
🪪  variable.undefined
 ------ --------------------------------------------- 

 ------ ---------------------------------------------- 
Line   templates/settings/warranty-emails-table.php
 ------ ---------------------------------------------- 
19     Variable $custom_vars might not be defined.   
🪪  variable.undefined                         
85     Variable $all_statuses might not be defined.  
🪪  variable.undefined                         
173    Variable $all_statuses might not be defined.  
🪪  variable.undefined                         
244    Variable $all_statuses might not be defined.  
🪪  variable.undefined
 ------ ---------------------------------------------- 

 ------ -------------------------------------------------------------------------------------------------------------------------------------------- 
Line   templates/shortcode-content.php
 ------ -------------------------------------------------------------------------------------------------------------------------------------------- 
14     <USER> <GROUP> @var has invalid value ($order_status string): Unexpected token "$order_status", expected type at offset 66 on line 4            
🪪  phpDoc.parseError                                                                                                                        
15     PHPDoc tag @var has invalid value ($order_has_warranty bool): Unexpected token "$order_has_warranty", expected type at offset 95 on line 5  
🪪  phpDoc.parseError                                                                                                                        
16     PHPDoc tag @var has invalid value ($args array): Unexpected token "$args", expected type at offset 128 on line 6                            
🪪  phpDoc.parseError                                                                                                                        
28     Variable $order_has_warranty might not be defined.                                                                                          
🪪  variable.undefined                                                                                                                       
28     Variable $order_status might not be defined.                                                                                                
🪪  variable.undefined                                                                                                                       
31     Variable $args might not be defined.                                                                                                        
🪪  variable.undefined                                                                                                                       
34     Variable $args might not be defined.                                                                                                        
🪪  variable.undefined
 ------ -------------------------------------------------------------------------------------------------------------------------------------------- 

 ------ ----------------------------------------------------------------------------------------------------------------------------- 
Line   templates/shortcode-order-items.php
 ------ ----------------------------------------------------------------------------------------------------------------------------- 
41     <USER> <GROUP> @var has invalid value ($items WC_Order_Item[]): Unexpected token "$items", expected type at offset 39 on line 4  
🪪  phpDoc.parseError                                                                                                         
43     Variable $items might not be defined.                                                                                        
🪪  variable.undefined
 ------ ----------------------------------------------------------------------------------------------------------------------------- 

 ------ ----------------------------------------------------------------------------------------------------------------------- 
Line   templates/shortcode-request-form.php
 ------ ----------------------------------------------------------------------------------------------------------------------- 
16     <USER> <GROUP> @var has invalid value ($order \WC_Order): Unexpected token "$order", expected type at offset 58 on line 4  
🪪  phpDoc.parseError                                                                                                   
17     PHPDoc tag @var has invalid value ($order_id int): Unexpected token "$order_id", expected type at offset 83 on line 5  
🪪  phpDoc.parseError                                                                                                   
18     PHPDoc tag @var has invalid value ($idxs array): Unexpected token "$idxs", expected type at offset 105 on line 6       
🪪  phpDoc.parseError                                                                                                   
53     Variable $idxs might not be defined.                                                                                   
🪪  variable.undefined                                                                                                  
55     Variable $order might not be defined.                                                                                  
🪪  variable.undefined                                                                                                  
60     Variable $order_id might not be defined.                                                                               
🪪  variable.undefined                                                                                                  
137    Variable $order might not be defined.                                                                                  
🪪  variable.undefined
 ------ ----------------------------------------------------------------------------------------------------------------------- 

 ------ ---------------------------------------------- 
Line   templates/shortcode-return-form.php
 ------ ---------------------------------------------- 
22     Variable $defaults might not be defined.      
🪪  variable.undefined                         
23     Variable $defaults might not be defined.      
🪪  variable.undefined                         
28     Variable $defaults might not be defined.      
🪪  variable.undefined                         
33     Variable $order_id might not be defined.      
🪪  variable.undefined                         
60     Variable $warranty_key might not be defined.  
🪪  variable.undefined
 ------ ---------------------------------------------- 

 ------ -------------------------------------------------- 
Line   templates/variables-panel-list.php
 ------ -------------------------------------------------- 
11     Variable $loop might not be defined.              
🪪  variable.undefined                             
14     Variable $loop might not be defined.              
🪪  variable.undefined                             
15     Variable $loop might not be defined.              
🪪  variable.undefined                             
15     Variable $loop might not be defined.              
🪪  variable.undefined                             
15     Variable $loop might not be defined.              
🪪  variable.undefined                             
15     Variable $warranty_default might not be defined.  
🪪  variable.undefined                             
21     Variable $loop might not be defined.              
🪪  variable.undefined                             
23     Variable $loop might not be defined.              
🪪  variable.undefined                             
23     Variable $loop might not be defined.              
🪪  variable.undefined                             
23     Variable $loop might not be defined.              
🪪  variable.undefined                             
24     Variable $warranty might not be defined.          
🪪  variable.undefined                             
25     Variable $warranty might not be defined.          
🪪  variable.undefined                             
26     Variable $warranty might not be defined.          
🪪  variable.undefined                             
31     Variable $loop might not be defined.              
🪪  variable.undefined                             
32     Variable $loop might not be defined.              
🪪  variable.undefined                             
32     Variable $loop might not be defined.              
🪪  variable.undefined                             
32     Variable $loop might not be defined.              
🪪  variable.undefined                             
32     Variable $warranty_label might not be defined.    
🪪  variable.undefined                             
36     Variable $loop might not be defined.              
🪪  variable.undefined                             
38     Variable $loop might not be defined.              
🪪  variable.undefined                             
40     Variable $loop might not be defined.              
🪪  variable.undefined                             
40     Variable $loop might not be defined.              
🪪  variable.undefined                             
40     Variable $loop might not be defined.              
🪪  variable.undefined                             
41     Variable $warranty might not be defined.          
🪪  variable.undefined                             
41     Variable $warranty might not be defined.          
🪪  variable.undefined                             
42     Variable $warranty might not be defined.          
🪪  variable.undefined                             
42     Variable $warranty might not be defined.          
🪪  variable.undefined                             
46     Variable $loop might not be defined.              
🪪  variable.undefined                             
47     Variable $loop might not be defined.              
🪪  variable.undefined                             
48     Variable $loop might not be defined.              
🪪  variable.undefined                             
48     Variable $loop might not be defined.              
🪪  variable.undefined                             
48     Variable $warranty might not be defined.          
🪪  variable.undefined                             
48     Variable $warranty might not be defined.          
🪪  variable.undefined                             
49     Variable $loop might not be defined.              
🪪  variable.undefined                             
49     Variable $loop might not be defined.              
🪪  variable.undefined                             
50     Variable $warranty might not be defined.          
🪪  variable.undefined                             
50     Variable $warranty might not be defined.          
🪪  variable.undefined                             
51     Variable $warranty might not be defined.          
🪪  variable.undefined                             
51     Variable $warranty might not be defined.          
🪪  variable.undefined                             
52     Variable $warranty might not be defined.          
🪪  variable.undefined                             
52     Variable $warranty might not be defined.          
🪪  variable.undefined                             
53     Variable $warranty might not be defined.          
🪪  variable.undefined                             
53     Variable $warranty might not be defined.          
🪪  variable.undefined                             
58     Variable $loop might not be defined.              
🪪  variable.undefined                             
60     Variable $loop might not be defined.              
🪪  variable.undefined                             
61     Variable $loop might not be defined.              
🪪  variable.undefined                             
61     Variable $loop might not be defined.              
🪪  variable.undefined                             
61     Variable $loop might not be defined.              
🪪  variable.undefined                             
77     Variable $loop might not be defined.              
🪪  variable.undefined                             
77     Variable $loop might not be defined.              
🪪  variable.undefined                             
81     Variable $loop might not be defined.              
🪪  variable.undefined                             
88     Variable $currency might not be defined.          
🪪  variable.undefined                             
89     Variable $loop might not be defined.              
🪪  variable.undefined                             
89     Variable $loop might not be defined.              
🪪  variable.undefined                             
92     Variable $loop might not be defined.              
🪪  variable.undefined                             
92     Variable $loop might not be defined.              
🪪  variable.undefined                             
93     Variable $loop might not be defined.              
🪪  variable.undefined                             
93     Variable $loop might not be defined.              
🪪  variable.undefined                             
100    Variable $loop might not be defined.              
🪪  variable.undefined                             
100    Variable $loop might not be defined.              
🪪  variable.undefined                             
100    Variable $loop might not be defined.              
🪪  variable.undefined
 ------ -------------------------------------------------- 


[ERROR] Found 335 errors


Script php -d memory_limit=2G ./vendor/bin/phpstan analyse --configuration=.phpstan/local-config.neon --level=2 handling the phpstan event returned with error code 1
