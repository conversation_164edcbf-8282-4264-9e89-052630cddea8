<?php
/**
 * The template for displaying warranty options.
 *
 * @package WooCommerce_Warranty\Templates
 * @version 2.0.0
 */

defined( 'ABSPATH' ) || exit;
?>

<?php
$get_data   = warranty_request_get_data();
$active_tab = ( ! empty( $get_data['tab'] ) ) ? $get_data['tab'] : 'general';
?>
<div class="wrap woocommerce">

	<h2><?php esc_html_e( 'Settings', 'woocommerce-warranty' ); ?></h2>

	<?php if ( ! empty( $get_data['updated'] ) ) : ?>
		<div id="message" class="updated fade">
			<p><?php esc_html_e( 'Settings saved', 'woocommerce-warranty' ); ?></p>
		</div>
	<?php endif; ?>

	<h2 class="nav-tab-wrapper woo-nav-tab-wrapper">
		<a href="admin.php?page=warranties-settings" class="nav-tab <?php echo 'general' === $active_tab ? 'nav-tab-active' : ''; ?>"><?php esc_html_e( 'General', 'woocommerce-warranty' ); ?></a>
		<a href="admin.php?page=warranties-settings&tab=default" class="nav-tab <?php echo 'default' === $active_tab ? 'nav-tab-active' : ''; ?>"><?php esc_html_e( 'Default Warranty', 'woocommerce-warranty' ); ?></a>
		<a href="admin.php?page=warranties-settings&tab=form" class="nav-tab <?php echo 'form' === $active_tab ? 'nav-tab-active' : ''; ?>"><?php esc_html_e( 'Warranty Form Builder', 'woocommerce-warranty' ); ?></a>
		<a href="admin.php?page=warranties-settings&tab=emails" class="nav-tab <?php echo 'emails' === $active_tab ? 'nav-tab-active' : ''; ?>"><?php esc_html_e( 'Notification Emails', 'woocommerce-warranty' ); ?></a>
		<a href="admin.php?page=warranties-settings&tab=permissions" class="nav-tab <?php echo 'permissions' === $active_tab ? 'nav-tab-active' : ''; ?>"><?php esc_html_e( 'Permissions', 'woocommerce-warranty' ); ?></a>
		<?php
		/**
		 * Hook to add more tabs button in the settings page.
		 *
		 * @since 1.6
		 */
		do_action( 'wc_warranty_settings_tabs' );
		?>
	</h2>

	<form action="admin-post.php" method="POST">
		<?php
		$settings = Warranty_Settings::get_settings_fields();

		switch ( $active_tab ) {
			case 'general':
				wp_enqueue_script( 'wc-warranty-admin-settings-general' );
				include WooCommerce_Warranty::$base_path . 'templates/settings/settings-general.php';
				break;

			case 'default':
				wp_enqueue_script( 'wc-warranty-admin-settings-default' );
				include WooCommerce_Warranty::$base_path . 'templates/settings/settings-default.php';
				break;

			case 'form':
				include WooCommerce_Warranty::$base_path . 'templates/settings/settings-form.php';
				break;

			case 'emails':
				include WooCommerce_Warranty::$base_path . 'templates/settings/settings-emails.php';
				break;

			case 'permissions':
				include WooCommerce_Warranty::$base_path . 'templates/settings/settings-permissions.php';
				break;
		}

		/**
		 * Hook to add more tabs content in the settings page.
		 *
		 * @since 1.6
		 */
		do_action( 'wc_warranty_settings_panels', $active_tab );
		?>

		<div class="submit">
			<input type="hidden" name="action" value="wc_warranty_settings_update" />
			<input type="hidden" name="tab" value="<?php echo esc_attr( $active_tab ); ?>" />
			<?php
			wp_nonce_field( 'wc_warranty_settings_save' );
			submit_button( __( 'Save changes', 'woocommerce-warranty' ) );
			?>
		</div>
	</form>

</div>
<script>
	jQuery( document ).ready( function( $ ) {
		$( '.woocommerce-help-tip' ).tipTip( { 'attribute': 'data-tip' } );
	} );
</script>
